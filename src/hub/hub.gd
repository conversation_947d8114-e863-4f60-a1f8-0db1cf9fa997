class_name <PERSON><PERSON>
extends Node2D

signal start_run

@export var portal: Portal
@export var meta_currency_label: Label
@export var player_service: PlayerService
@export var tile_query_system: TileQuerySystem

func _ready() -> void:
	portal.player_entered.connect(_on_portal_player_entered)
	_update_meta_currency_display()

func _on_portal_player_entered() -> void:
	start_run.emit()

func _update_meta_currency_display() -> void:
	var currency: int = GameProgress.get_meta_currency()
	meta_currency_label.text = "Мета-валюта: " + str(currency)

func setup_and_spawn_player(player_scene: PackedScene) -> void:
	var spawn_tile: Node2D = tile_query_system.get_random_tile()
	if not is_instance_valid(spawn_tile):
		push_error("Level: No spawnable tiles found.")
		return

	var player_instance: Node2D = player_scene.instantiate() as Node2D
	if not is_instance_valid(player_instance):
		push_error("Level: Failed to instantiate player scene.")
		return

	add_child(player_instance)
	player_instance.global_position = spawn_tile.global_position

	player_service.initialize(player_instance)
