class_name TileQuerySystem
extends Node

var _tile_map: Dictionary = {}

func _ready() -> void:
	_build_tile_map.call_deferred()

func get_tile_at_global_pos(position: Vector2) -> Node2D:
	return _tile_map.get(position, null)

func get_nearest_tile(position: Vector2) -> Node2D:
	if _tile_map.is_empty():
		return null

	var nearest_tile: Node2D = null
	var min_dist_sq: float = INF

	for tile_pos: Vector2 in _tile_map.keys():
		var dist_sq: float = position.distance_squared_to(tile_pos)
		if dist_sq < min_dist_sq:
			min_dist_sq = dist_sq
			nearest_tile = _tile_map[tile_pos]

	return nearest_tile

func get_random_tile() -> Node2D:
	if _tile_map.is_empty():
		return null

	var keys := _tile_map.keys()
	var random_index := randi() % keys.size()
	return _tile_map[keys[random_index]]

func _build_tile_map() -> void:
	_tile_map.clear()
	var tiles: Array[Node] = get_tree().get_nodes_in_group(&"tiles")
	for tile_node: Node2D in tiles:
		_tile_map[tile_node.global_position] = tile_node
